# Redis 集群/哨兵模式 CRUD Demo

这是一个基于Spring Boot的Redis多模式连接和CRUD操作演示项目，支持Redis集群模式和哨兵模式。

## 功能特性

- **多模式支持**: 支持Redis集群模式和哨兵模式，可通过配置切换
- **自动故障转移**: Redis Sentinel集群自动故障转移
- **高可用集群**: Redis Cluster集群模式支持
- **完整的CRUD操作**: 创建、读取、更新、删除
- **用户搜索功能**: 支持按用户名搜索
- **批量操作**: 批量创建和删除用户
- **缓存过期时间管理**: 灵活的缓存过期策略
- **RESTful API接口**: 标准的REST API设计
- **连接池优化**: 优化的连接池配置

## 技术栈

- Spring Boot 3.5.5
- Redis Sentinel / Redis Cluster
- Lettuce客户端 (支持连接池)
- Jackson JSON序列化
- Apache Commons Pool2 (连接池管理)

## 项目结构

```
src/main/java/com/trinasloar/gateway/zeuspaasyz/
├── config/
│   ├── RedisConfig.java                    # Redis配置类（支持集群和哨兵模式）
│   └── RedisProperties.java               # Redis配置属性类
├── controller/
│   ├── UserController.java                # REST API控制器
│   └── HealthController.java              # 健康检查控制器
├── entity/
│   └── User.java                          # 用户实体类
├── service/
│   └── RedisService.java                  # Redis服务类
└── ZeusPaasYzApplication.java             # Spring Boot启动类
```

## 配置说明

### 1. Redis模式配置

项目支持两种Redis模式：**哨兵模式(Sentinel)** 和 **集群模式(Cluster)**，可通过配置文件进行切换。

在 `application.yml` 中配置Redis相关参数：

```yaml
redis:
  # Redis模式选择: sentinel(哨兵模式) 或 cluster(集群模式)
  mode: sentinel

  # 通用配置
  password: Ab@123456
  database: 0
  timeout: 2000ms

  # 连接池配置
  pool:
    max-active: 8
    max-idle: 8
    min-idle: 0
    max-wait: -1ms

  # 哨兵模式配置
  sentinel:
    master: mymaster
    nodes: tasp-traffic.trinasolar.com:31014

  # 集群模式配置
  cluster:
    nodes:
      - 127.0.0.1:7000
      - 127.0.0.1:7001
      - 127.0.0.1:7002
      - 127.0.0.1:7003
      - 127.0.0.1:7004
      - 127.0.0.1:7005
    max-redirects: 3
```

### 2. 哨兵模式配置详解

```yaml
redis:
  mode: sentinel                    # 设置为哨兵模式
  password: your_password_here      # Redis密码（如果有）
  database: 0                      # Redis数据库索引
  timeout: 2000ms                  # 连接超时时间

  sentinel:
    master: mymaster               # Sentinel主节点名称
    nodes: 127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381  # Sentinel节点列表
```

### 3. 集群模式配置详解

```yaml
redis:
  mode: cluster                     # 设置为集群模式
  password: your_password_here      # Redis密码（如果有）
  timeout: 2000ms                  # 连接超时时间

  cluster:
    nodes:                         # 集群节点列表
      - 127.0.0.1:7000
      - 127.0.0.1:7001
      - 127.0.0.1:7002
      - 127.0.0.1:7003
      - 127.0.0.1:7004
      - 127.0.0.1:7005
    max-redirects: 3               # 最大重定向次数
```

### 4. 连接池配置详解

```yaml
redis:
  pool:
    max-active: 8                  # 最大活跃连接数
    max-idle: 8                    # 最大空闲连接数
    min-idle: 0                    # 最小空闲连接数
    max-wait: -1ms                 # 最大等待时间(-1表示无限等待)
```

## Redis环境搭建

### 1. Redis Sentinel集群搭建

确保你的Redis Sentinel集群已经正确配置和运行。你需要：

1. 启动多个Redis实例（主从模式）
2. 配置Sentinel监控这些Redis实例
3. 确保Sentinel节点能够相互通信

## API接口文档

### 创建用户
```http
POST /api/users
Content-Type: application/json

{
    "id": 1,
    "username": "john_doe",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "age": 30,
    "address": "北京市朝阳区"
}
```

### 获取用户
```http
GET /api/users/{id}
```

### 更新用户
```http
PUT /api/users/{id}
Content-Type: application/json

{
    "username": "john_updated",
    "email": "<EMAIL>",
    "age": 31
}
```

### 删除用户
```http
DELETE /api/users/{id}
```

### 获取所有用户
```http
GET /api/users
```

### 搜索用户
```http
GET /api/users/search?username=john
```

### 批量创建用户
```http
POST /api/users/batch
Content-Type: application/json

[
    {
        "id": 2,
        "username": "jane_doe",
        "email": "<EMAIL>"
    },
    {
        "id": 3,
        "username": "bob_smith",
        "email": "<EMAIL>"
    }
]
```

### 设置用户过期时间
```http
POST /api/users/{id}/expire?days=7
```

### 获取用户过期时间
```http
GET /api/users/{id}/expire
```

### 检查用户是否存在
```http
GET /api/users/{id}/exists
```

### 获取用户统计信息
```http
GET /api/users/stats
```

## 运行项目

1. 确保Redis Sentinel集群正常运行
2. 修改 `application.properties` 中的Sentinel配置
3. 运行Spring Boot应用：

```bash
./mvnw spring-boot:run
```

或者

```bash
mvn spring-boot:run
```

## 测试示例

### 使用curl测试

1. 创建用户：
```bash
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "age": 25
  }'
```

2. 获取用户：
```bash
curl http://localhost:8080/api/users/1
```

3. 更新用户：
```bash
curl -X PUT http://localhost:8080/api/users/1 \
  -H "Content-Type: application/json" \
  -d '{
    "username": "updateduser",
    "age": 26
  }'
```

4. 删除用户：
```bash
curl -X DELETE http://localhost:8080/api/users/1
```

### 使用Postman测试

导入以下请求到Postman：

- Method: POST, URL: `http://localhost:8080/api/users`
- Method: GET, URL: `http://localhost:8080/api/users/1`
- Method: PUT, URL: `http://localhost:8080/api/users/1`
- Method: DELETE, URL: `http://localhost:8080/api/users/1`
- Method: GET, URL: `http://localhost:8080/api/users`

## 注意事项

1. **密码配置**：如果Redis集群没有密码保护，请删除 `spring.redis.password` 配置或设置为空值
2. **网络连接**：确保应用服务器能够访问到所有的Sentinel节点
3. **防火墙**：确保Redis端口（6379）和Sentinel端口（26379等）没有被防火墙阻止
4. **序列化**：使用了JSON序列化方式存储对象，确保对象实现了Serializable接口
5. **过期时间**：默认设置了7天的过期时间，可以通过API动态修改

## 故障排除

### 常见错误

1. **Connection refused**: 检查Redis Sentinel节点地址和端口是否正确
2. **Authentication failed**: 检查Redis密码配置
3. **No reachable node**: 确保至少有一个Sentinel节点可访问
4. **Master not found**: 检查Sentinel配置中的master名称是否正确

### 日志调试

启用DEBUG日志查看详细的连接信息：

```properties
logging.level.org.springframework.data.redis=DEBUG
logging.level.redis.clients.jedis=DEBUG
```

## 使用说明

### 1. 快速启动

#### 使用哨兵模式启动
```bash
# 使用默认配置（哨兵模式）
java -jar zeus-paas-yz-0.0.1-SNAPSHOT.jar

# 或者明确指定哨兵模式配置
java -jar zeus-paas-yz-0.0.1-SNAPSHOT.jar --spring.profiles.active=sentinel
```

#### 使用集群模式启动
```bash
# 使用集群模式配置
java -jar zeus-paas-yz-0.0.1-SNAPSHOT.jar --spring.profiles.active=cluster
```

### 2. 配置文件说明

项目提供了三个配置文件：
- `application.yml`: 默认配置（哨兵模式）
- `application-sentinel.yml`: 哨兵模式配置示例
- `application-cluster.yml`: 集群模式配置示例

### 3. 模式切换

只需要修改配置文件中的 `redis.mode` 参数：
```yaml
redis:
  mode: sentinel  # 哨兵模式
  # 或
  mode: cluster   # 集群模式
```

### 4. 运行测试

```bash
# 运行所有测试
mvn test

# 运行Redis配置测试
mvn test -Dtest=RedisConfigTest

# 运行哨兵模式测试
mvn test -Dtest=RedisSentinelModeTest

# 运行集群模式测试
mvn test -Dtest=RedisClusterModeTest
```

## 故障排除

### 1. 常见连接问题

**哨兵模式问题:**
- **Connection refused**: 检查Sentinel节点地址和端口是否正确
- **Master not found**: 检查Sentinel配置中的master名称是否正确
- **Authentication failed**: 检查Redis密码配置

**集群模式问题:**
- **No reachable node**: 确保至少有一个集群节点可访问
- **MOVED/ASK redirections**: 检查max-redirects配置是否合理
- **Cluster down**: 确保集群状态正常

### 2. 性能优化建议

**连接池配置优化:**
```yaml
redis:
  pool:
    max-active: 16      # 根据并发量调整
    max-idle: 8         # 保持适当的空闲连接
    min-idle: 2         # 预热连接池
    max-wait: 3000ms    # 避免无限等待
```

**超时配置优化:**
```yaml
redis:
  timeout: 3000ms       # 根据网络延迟调整
```

### 3. 监控和日志

启用详细日志进行调试：
```yaml
logging:
  level:
    com.trinasloar.gateway.zeuspaasyz: DEBUG
    org.springframework.data.redis: DEBUG
    io.lettuce.core: DEBUG
```

## 扩展功能

你可以基于这个项目进行以下扩展：

1. **多数据源支持**: 同时连接多个Redis集群
2. **缓存注解**: 添加Spring Cache注解支持
3. **分布式锁**: 实现基于Redis的分布式锁
4. **发布订阅**: 实现Redis消息发布订阅功能
5. **监控集成**: 集成Micrometer进行性能监控
6. **健康检查**: 添加Redis连接健康检查端点
7. **数据迁移**: 实现哨兵模式和集群模式之间的数据迁移工具

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
