package com.trinasloar.gateway.zeuspaasyz;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.JedisPoolConfig;

import java.util.HashSet;
import java.util.Set;

public class RedisExample {

    private Jedis jedis;
    private JedisSentinelPool sentinelPool; // 哨兵连接池
    
    public static void main(String[] args) {
        // 原有的直接连接方式
        RedisExample example = new RedisExample("tasp-traffic.trinasolar.com", 31017);
        
        // 使用哨兵模式的示例
        RedisExample sentinelExample = new RedisExample();
        sentinelExample.initSentinelMode();
        sentinelExample.sentinelSetKey("sentinelKey", "sentinelValue");
        System.out.println("Sentinel Key: sentinelKey, Value: " + sentinelExample.sentinelGetKey("sentinelKey"));

        example.setKey("myKey", "myValue");
        example.setKey("myKey2", "abc");
        System.out.println("Key: myKey, Value: " + example.getKey("myKey"));
        System.out.println("Key: myKey2, Value: " + example.getKey("myKey2"));
        example.deleteKey("myKey");
        example.deleteKey("myKey2");
        System.out.println("Delete the keys successful.");
        sentinelExample.closeSentinel();
    }
    
    public RedisExample(String host, int port) {
        this.jedis = new Jedis(host, port);
       // jedis.auth("WMSAdmin@123");
    }
    
    // 无参构造函数用于哨兵模式
    public RedisExample() {
    }

    /**
     * 初始化哨兵模式连接
     */
    public void initSentinelMode() {
        // 哨兵配置
        Set<String> sentinels = new HashSet<>();
        sentinels.add("tasp-traffic.trinasolar.com:31091");  // 替换为实际的哨兵地址
        
        // 连接池配置
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(20);
        poolConfig.setMaxIdle(10);
        poolConfig.setMinIdle(5);
        // 创建哨兵连接池
        sentinelPool = new JedisSentinelPool(
            "mymaster",      // 主节点名称
            sentinels,       // 哨兵节点集合
            poolConfig,      // 连接池配置
            2000,            // 连接超时时间
            "WMSAdmin@123"
        );
    }

    /**
     * 通过哨兵模式设置键值对
     */
    public void sentinelSetKey(String key, String value) {
        try (Jedis jedis = sentinelPool.getResource()) {
            jedis.set(key, value);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 通过哨兵模式获取键对应的值
     */
    public String sentinelGetKey(String key) {
        try (Jedis jedis = sentinelPool.getResource()) {
            return jedis.get(key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 通过哨兵模式设置带过期时间的键值对（单位：秒）
     */
    public void sentinelSetKeyWithExpiry(String key, String value, int seconds) {
        try (Jedis jedis = sentinelPool.getResource()) {
            jedis.setex(key, seconds, value);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 通过哨兵模式检查键是否存在
     */
    public boolean sentinelIsKeyExists(String key) {
        try (Jedis jedis = sentinelPool.getResource()) {
            return jedis.exists(key);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 关闭哨兵连接池
     */
    public void closeSentinel() {
        if (sentinelPool != null) {
            sentinelPool.close();
        }
    }

    /**
     * 设置键值对
     */
    public void setKey(String key, String value) {
        jedis.set(key, value);
    }

    /**
     * 获取键对应的值
     */
    public String getKey(String key) {
        return jedis.get(key);
    }

    /**
     * 删除指定的键
     */
    public void deleteKey(String key) {
        jedis.del(key);
    }

    /**
     * 设置带过期时间的键值对（单位：秒）
     */
    public void setKeyWithExpiry(String key, String value, int seconds) {
        jedis.setex(key, seconds, value);
    }

    /**
     * 检查键是否存在
     */
    public boolean isKeyExists(String key) {
        return jedis.exists(key);
    }

    /**
     * 关闭 Redis 连接
     */
    public void close() {
        jedis.close();
    }
}
