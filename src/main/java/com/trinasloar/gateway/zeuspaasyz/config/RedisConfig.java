package com.trinasloar.gateway.zeuspaasyz.config;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

@Configuration
public class RedisConfig {

    @Autowired
    private RedisProperties redisProperties;

    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        String mode = redisProperties.getMode();

        // 创建连接池配置
        GenericObjectPoolConfig<Object> poolConfig = createPoolConfig();

        // 创建Lettuce客户端配置
        LettuceClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
                .poolConfig(poolConfig)
                .commandTimeout(Duration.ofMillis(redisProperties.getTimeout()))
                .build();

        if ("cluster".equalsIgnoreCase(mode)) {
            return createClusterConnectionFactory(clientConfig);
        } else {
            return createSentinelConnectionFactory(clientConfig);
        }
    }

    /**
     * 创建连接池配置
     */
    private GenericObjectPoolConfig<Object> createPoolConfig() {
        GenericObjectPoolConfig<Object> poolConfig = new GenericObjectPoolConfig<>();
        RedisProperties.Pool pool = redisProperties.getPool();

        poolConfig.setMaxTotal(pool.getMaxActive());
        poolConfig.setMaxIdle(pool.getMaxIdle());
        poolConfig.setMinIdle(pool.getMinIdle());
        poolConfig.setMaxWaitMillis(pool.getMaxWait());

        // 设置连接池的其他参数
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(false);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setTimeBetweenEvictionRunsMillis(30000);
        poolConfig.setMinEvictableIdleTimeMillis(60000);
        poolConfig.setNumTestsPerEvictionRun(3);

        return poolConfig;
    }

    /**
     * 创建Redis集群连接工厂
     */
    private RedisConnectionFactory createClusterConnectionFactory(LettuceClientConfiguration clientConfig) {
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();

        // 设置集群节点
        List<String> clusterNodes = redisProperties.getCluster().getNodes();
        if (clusterNodes != null && !clusterNodes.isEmpty()) {
            for (String node : clusterNodes) {
                String[] hostPort = node.split(":");
                if (hostPort.length == 2) {
                    clusterConfig.clusterNode(hostPort[0], Integer.parseInt(hostPort[1]));
                }
            }
        }

        // 设置最大重定向次数
        clusterConfig.setMaxRedirects(redisProperties.getCluster().getMaxRedirects());

        // 设置密码
        String password = redisProperties.getPassword();
        if (password != null && !password.isEmpty()) {
            clusterConfig.setPassword(password);
        }

        return new LettuceConnectionFactory(clusterConfig, clientConfig);
    }

    /**
     * 创建Redis哨兵连接工厂
     */
    private RedisConnectionFactory createSentinelConnectionFactory(LettuceClientConfiguration clientConfig) {
        String master = redisProperties.getSentinel().getMaster();
        String sentinelNodes = redisProperties.getSentinel().getNodes();

        RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration()
                .master(master);

        // 解析Sentinel节点
        if (sentinelNodes != null && !sentinelNodes.isEmpty()) {
            Arrays.stream(sentinelNodes.split(","))
                    .map(String::trim)
                    .forEach(node -> {
                        String[] hostPort = node.split(":");
                        if (hostPort.length == 2) {
                            sentinelConfig.sentinel(hostPort[0], Integer.parseInt(hostPort[1]));
                        }
                    });
        }

        // 设置密码
        String password = redisProperties.getPassword();
        if (password != null && !password.isEmpty()) {
            sentinelConfig.setPassword(password);
        }

        // 设置数据库
        sentinelConfig.setDatabase(redisProperties.getDatabase());

        return new LettuceConnectionFactory(sentinelConfig, clientConfig);
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 使用String序列化器作为key序列化器
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());

        // 使用JSON序列化器作为value序列化器
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());

        template.afterPropertiesSet();
        return template;
    }
}
