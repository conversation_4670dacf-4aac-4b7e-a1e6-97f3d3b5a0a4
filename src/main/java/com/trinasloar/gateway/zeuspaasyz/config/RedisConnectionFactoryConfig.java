package com.trinasloar.gateway.zeuspaasyz.config;

import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

/**
 * Redis连接工厂配置类
 * 提供更详细的连接池和客户端配置
 */
@Configuration
public class RedisConnectionFactoryConfig {

    @Autowired
    private RedisProperties redisProperties;

    /**
     * 创建Lettuce客户端资源
     */
    @Bean(destroyMethod = "shutdown")
    public ClientResources clientResources() {
        return DefaultClientResources.create();
    }

    /**
     * 创建连接池配置
     */
    @Bean
    public GenericObjectPoolConfig<Object> redisPoolConfig() {
        GenericObjectPoolConfig<Object> poolConfig = new GenericObjectPoolConfig<>();
        RedisProperties.Pool pool = redisProperties.getPool();
        
        poolConfig.setMaxTotal(pool.getMaxActive());
        poolConfig.setMaxIdle(pool.getMaxIdle());
        poolConfig.setMinIdle(pool.getMinIdle());
        poolConfig.setMaxWaitMillis(pool.getMaxWait());
        
        // 设置连接池的其他参数
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(false);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setTimeBetweenEvictionRunsMillis(30000);
        poolConfig.setMinEvictableIdleTimeMillis(60000);
        poolConfig.setNumTestsPerEvictionRun(3);
        
        return poolConfig;
    }

    /**
     * 创建Lettuce客户端配置
     */
    @Bean
    public LettuceClientConfiguration lettuceClientConfiguration(
            ClientResources clientResources,
            GenericObjectPoolConfig<Object> poolConfig) {
        
        return LettucePoolingClientConfiguration.builder()
                .poolConfig(poolConfig)
                .clientResources(clientResources)
                .commandTimeout(Duration.ofMillis(redisProperties.getTimeout()))
                .build();
    }

    /**
     * 创建增强的Redis连接工厂
     */
    @Bean
    public RedisConnectionFactory enhancedRedisConnectionFactory(
            LettuceClientConfiguration clientConfiguration) {
        
        String mode = redisProperties.getMode();
        
        if ("cluster".equalsIgnoreCase(mode)) {
            return createEnhancedClusterConnectionFactory(clientConfiguration);
        } else {
            return createEnhancedSentinelConnectionFactory(clientConfiguration);
        }
    }

    /**
     * 创建增强的Redis集群连接工厂
     */
    private RedisConnectionFactory createEnhancedClusterConnectionFactory(
            LettuceClientConfiguration clientConfiguration) {
        
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();
        
        // 设置集群节点
        List<String> clusterNodes = redisProperties.getCluster().getNodes();
        if (clusterNodes != null && !clusterNodes.isEmpty()) {
            for (String node : clusterNodes) {
                String[] hostPort = node.split(":");
                if (hostPort.length == 2) {
                    clusterConfig.clusterNode(hostPort[0], Integer.parseInt(hostPort[1]));
                }
            }
        }
        
        // 设置最大重定向次数
        clusterConfig.setMaxRedirects(redisProperties.getCluster().getMaxRedirects());
        
        // 设置密码
        String password = redisProperties.getPassword();
        if (password != null && !password.isEmpty()) {
            clusterConfig.setPassword(password);
        }
        
        LettuceConnectionFactory factory = new LettuceConnectionFactory(clusterConfig, clientConfiguration);
        factory.setValidateConnection(true);
        factory.setShareNativeConnection(false);
        
        return factory;
    }

    /**
     * 创建增强的Redis哨兵连接工厂
     */
    private RedisConnectionFactory createEnhancedSentinelConnectionFactory(
            LettuceClientConfiguration clientConfiguration) {
        
        String master = redisProperties.getSentinel().getMaster();
        String sentinelNodes = redisProperties.getSentinel().getNodes();
        
        RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration()
                .master(master);

        // 解析Sentinel节点
        if (sentinelNodes != null && !sentinelNodes.isEmpty()) {
            Arrays.stream(sentinelNodes.split(","))
                    .map(String::trim)
                    .forEach(node -> {
                        String[] hostPort = node.split(":");
                        if (hostPort.length == 2) {
                            sentinelConfig.sentinel(hostPort[0], Integer.parseInt(hostPort[1]));
                        }
                    });
        }

        // 设置密码
        String password = redisProperties.getPassword();
        if (password != null && !password.isEmpty()) {
            sentinelConfig.setPassword(password);
        }

        // 设置数据库
        sentinelConfig.setDatabase(redisProperties.getDatabase());

        LettuceConnectionFactory factory = new LettuceConnectionFactory(sentinelConfig, clientConfiguration);
        factory.setValidateConnection(true);
        factory.setShareNativeConnection(false);
        
        return factory;
    }
}
