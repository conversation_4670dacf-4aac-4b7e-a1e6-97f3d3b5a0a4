package com.trinasloar.gateway.zeuspaasyz.controller;

import com.trinasloar.gateway.zeuspaasyz.config.RedisProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 提供Redis连接状态和配置信息的检查接口
 */
@RestController
@RequestMapping("/api/health")
public class HealthController {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisProperties redisProperties;

    /**
     * 检查Redis连接状态
     * GET /api/health/redis
     */
    @GetMapping("/redis")
    public ResponseEntity<Map<String, Object>> checkRedisHealth() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 测试Redis连接
            String testKey = "health:check:" + System.currentTimeMillis();
            String testValue = "OK";
            
            redisTemplate.opsForValue().set(testKey, testValue);
            String retrievedValue = (String) redisTemplate.opsForValue().get(testKey);
            redisTemplate.delete(testKey);
            
            if ("OK".equals(retrievedValue)) {
                response.put("status", "UP");
                response.put("message", "Redis连接正常");
                response.put("mode", redisProperties.getMode());
                response.put("connectionFactory", redisConnectionFactory.getClass().getSimpleName());
                
                // 添加配置信息
                Map<String, Object> config = new HashMap<>();
                config.put("mode", redisProperties.getMode());
                config.put("database", redisProperties.getDatabase());
                config.put("timeout", redisProperties.getTimeout() + "ms");
                
                if ("sentinel".equalsIgnoreCase(redisProperties.getMode())) {
                    config.put("master", redisProperties.getSentinel().getMaster());
                    config.put("sentinelNodes", redisProperties.getSentinel().getNodes());
                } else if ("cluster".equalsIgnoreCase(redisProperties.getMode())) {
                    config.put("clusterNodes", redisProperties.getCluster().getNodes());
                    config.put("maxRedirects", redisProperties.getCluster().getMaxRedirects());
                }
                
                response.put("config", config);
                
                return ResponseEntity.ok(response);
            } else {
                response.put("status", "DOWN");
                response.put("message", "Redis读写测试失败");
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
            }
            
        } catch (Exception e) {
            response.put("status", "DOWN");
            response.put("message", "Redis连接失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
        }
    }

    /**
     * 获取Redis配置信息
     * GET /api/health/redis/config
     */
    @GetMapping("/redis/config")
    public ResponseEntity<Map<String, Object>> getRedisConfig() {
        Map<String, Object> response = new HashMap<>();
        
        response.put("mode", redisProperties.getMode());
        response.put("database", redisProperties.getDatabase());
        response.put("timeout", redisProperties.getTimeout());
        
        // 连接池配置
        Map<String, Object> poolConfig = new HashMap<>();
        poolConfig.put("maxActive", redisProperties.getPool().getMaxActive());
        poolConfig.put("maxIdle", redisProperties.getPool().getMaxIdle());
        poolConfig.put("minIdle", redisProperties.getPool().getMinIdle());
        poolConfig.put("maxWait", redisProperties.getPool().getMaxWait());
        response.put("pool", poolConfig);
        
        // 模式特定配置
        if ("sentinel".equalsIgnoreCase(redisProperties.getMode())) {
            Map<String, Object> sentinelConfig = new HashMap<>();
            sentinelConfig.put("master", redisProperties.getSentinel().getMaster());
            sentinelConfig.put("nodes", redisProperties.getSentinel().getNodes());
            response.put("sentinel", sentinelConfig);
        } else if ("cluster".equalsIgnoreCase(redisProperties.getMode())) {
            Map<String, Object> clusterConfig = new HashMap<>();
            clusterConfig.put("nodes", redisProperties.getCluster().getNodes());
            clusterConfig.put("maxRedirects", redisProperties.getCluster().getMaxRedirects());
            response.put("cluster", clusterConfig);
        }
        
        response.put("connectionFactory", redisConnectionFactory.getClass().getSimpleName());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 应用整体健康检查
     * GET /api/health
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> checkOverallHealth() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 检查Redis连接
            ResponseEntity<Map<String, Object>> redisHealth = checkRedisHealth();
            Map<String, Object> redisStatus = redisHealth.getBody();
            
            response.put("application", "zeus-paas-yz");
            response.put("redis", redisStatus);
            
            if ("UP".equals(redisStatus.get("status"))) {
                response.put("status", "UP");
                response.put("message", "应用运行正常");
                return ResponseEntity.ok(response);
            } else {
                response.put("status", "DOWN");
                response.put("message", "Redis连接异常");
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
            }
            
        } catch (Exception e) {
            response.put("status", "DOWN");
            response.put("message", "健康检查失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
