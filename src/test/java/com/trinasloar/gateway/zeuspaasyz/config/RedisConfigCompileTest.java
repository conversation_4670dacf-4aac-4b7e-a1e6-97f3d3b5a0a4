package com.trinasloar.gateway.zeuspaasyz.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis配置编译测试
 * 验证配置类是否能正确加载和编译
 */
@SpringBootTest
public class RedisConfigCompileTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private RedisProperties redisProperties;

    /**
     * 测试Spring上下文是否正确加载
     */
    @Test
    public void testApplicationContextLoads() {
        assertNotNull(applicationContext, "Spring应用上下文应该正确加载");
        System.out.println("✅ Spring应用上下文加载成功");
    }

    /**
     * 测试Redis配置属性是否正确注入
     */
    @Test
    public void testRedisPropertiesLoaded() {
        assertNotNull(redisProperties, "Redis配置属性应该正确注入");
        assertNotNull(redisProperties.getMode(), "Redis模式不应为空");
        
        System.out.println("✅ Redis配置属性加载成功");
        System.out.println("Redis模式: " + redisProperties.getMode());
        System.out.println("数据库: " + redisProperties.getDatabase());
        System.out.println("超时时间: " + redisProperties.getTimeout() + "ms");
    }

    /**
     * 测试Redis连接工厂Bean是否正确创建
     */
    @Test
    public void testRedisConnectionFactoryBean() {
        assertTrue(applicationContext.containsBean("redisConnectionFactory"), 
                  "应该包含redisConnectionFactory Bean");
        
        RedisConnectionFactory connectionFactory = applicationContext.getBean(RedisConnectionFactory.class);
        assertNotNull(connectionFactory, "Redis连接工厂不应为空");
        
        System.out.println("✅ Redis连接工厂Bean创建成功");
        System.out.println("连接工厂类型: " + connectionFactory.getClass().getSimpleName());
    }

    /**
     * 测试RedisTemplate Bean是否正确创建
     */
    @Test
    public void testRedisTemplateBean() {
        assertTrue(applicationContext.containsBean("redisTemplate"), 
                  "应该包含redisTemplate Bean");
        
        @SuppressWarnings("unchecked")
        RedisTemplate<String, Object> redisTemplate = 
            (RedisTemplate<String, Object>) applicationContext.getBean("redisTemplate");
        assertNotNull(redisTemplate, "RedisTemplate不应为空");
        assertNotNull(redisTemplate.getConnectionFactory(), "RedisTemplate连接工厂不应为空");
        
        System.out.println("✅ RedisTemplate Bean创建成功");
    }

    /**
     * 测试配置类是否正确加载
     */
    @Test
    public void testRedisConfigBean() {
        assertTrue(applicationContext.containsBean("redisConfig"), 
                  "应该包含redisConfig Bean");
        
        RedisConfig redisConfig = applicationContext.getBean(RedisConfig.class);
        assertNotNull(redisConfig, "Redis配置类不应为空");
        
        System.out.println("✅ Redis配置类Bean创建成功");
    }

    /**
     * 测试不同模式的配置
     */
    @Test
    public void testModeConfiguration() {
        String mode = redisProperties.getMode();
        assertTrue("sentinel".equalsIgnoreCase(mode) || "cluster".equalsIgnoreCase(mode),
                  "Redis模式应该是sentinel或cluster");
        
        if ("sentinel".equalsIgnoreCase(mode)) {
            assertNotNull(redisProperties.getSentinel(), "哨兵配置不应为空");
            System.out.println("✅ 哨兵模式配置验证通过");
        } else if ("cluster".equalsIgnoreCase(mode)) {
            assertNotNull(redisProperties.getCluster(), "集群配置不应为空");
            System.out.println("✅ 集群模式配置验证通过");
        }
    }

    /**
     * 测试连接池配置
     */
    @Test
    public void testPoolConfiguration() {
        RedisProperties.Pool pool = redisProperties.getPool();
        assertNotNull(pool, "连接池配置不应为空");
        assertTrue(pool.getMaxActive() > 0, "最大活跃连接数应该大于0");
        assertTrue(pool.getMaxIdle() >= 0, "最大空闲连接数应该大于等于0");
        assertTrue(pool.getMinIdle() >= 0, "最小空闲连接数应该大于等于0");
        
        System.out.println("✅ 连接池配置验证通过");
        System.out.println("最大活跃连接: " + pool.getMaxActive());
        System.out.println("最大空闲连接: " + pool.getMaxIdle());
        System.out.println("最小空闲连接: " + pool.getMinIdle());
    }
}
