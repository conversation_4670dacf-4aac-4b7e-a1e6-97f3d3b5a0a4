package com.trinasloar.gateway.zeuspaasyz.config;

import com.trinasloar.gateway.zeuspaasyz.entity.User;
import com.trinasloar.gateway.zeuspaasyz.service.RedisService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis配置测试类
 * 测试Redis集群模式和哨兵模式的配置和功能
 */
@SpringBootTest
public class RedisConfigTest {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedisProperties redisProperties;

    /**
     * 测试Redis连接工厂是否正确创建
     */
    @Test
    public void testRedisConnectionFactory() {
        assertNotNull(redisConnectionFactory, "Redis连接工厂不应为空");
        System.out.println("Redis连接工厂类型: " + redisConnectionFactory.getClass().getSimpleName());
        System.out.println("Redis模式: " + redisProperties.getMode());
    }

    /**
     * 测试RedisTemplate是否正确配置
     */
    @Test
    public void testRedisTemplate() {
        assertNotNull(redisTemplate, "RedisTemplate不应为空");
        assertNotNull(redisTemplate.getConnectionFactory(), "RedisTemplate连接工厂不应为空");
        
        // 测试基本的Redis操作
        String testKey = "test:config";
        String testValue = "Redis配置测试";
        
        redisTemplate.opsForValue().set(testKey, testValue);
        String retrievedValue = (String) redisTemplate.opsForValue().get(testKey);
        
        assertEquals(testValue, retrievedValue, "Redis读写测试失败");
        
        // 清理测试数据
        redisTemplate.delete(testKey);
    }

    /**
     * 测试Redis配置属性
     */
    @Test
    public void testRedisProperties() {
        assertNotNull(redisProperties, "Redis配置属性不应为空");
        assertNotNull(redisProperties.getMode(), "Redis模式不应为空");
        
        System.out.println("Redis配置信息:");
        System.out.println("模式: " + redisProperties.getMode());
        System.out.println("数据库: " + redisProperties.getDatabase());
        System.out.println("超时时间: " + redisProperties.getTimeout() + "ms");
        
        if ("sentinel".equalsIgnoreCase(redisProperties.getMode())) {
            System.out.println("哨兵主节点: " + redisProperties.getSentinel().getMaster());
            System.out.println("哨兵节点: " + redisProperties.getSentinel().getNodes());
        } else if ("cluster".equalsIgnoreCase(redisProperties.getMode())) {
            System.out.println("集群节点: " + redisProperties.getCluster().getNodes());
            System.out.println("最大重定向: " + redisProperties.getCluster().getMaxRedirects());
        }
    }

    /**
     * 测试Redis服务的CRUD操作
     */
    @Test
    public void testRedisServiceCRUD() {
        // 创建测试用户
        User testUser = new User();
        testUser.setId(999L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setPhone("13800138000");
        testUser.setAge(25);
        testUser.setAddress("测试地址");

        // 测试创建用户
        boolean createResult = redisService.createUser(testUser);
        assertTrue(createResult, "创建用户应该成功");

        // 测试读取用户
        User retrievedUser = redisService.getUserById(999L);
        assertNotNull(retrievedUser, "读取用户不应为空");
        assertEquals(testUser.getUsername(), retrievedUser.getUsername(), "用户名应该匹配");
        assertEquals(testUser.getEmail(), retrievedUser.getEmail(), "邮箱应该匹配");

        // 测试更新用户
        testUser.setAge(26);
        boolean updateResult = redisService.updateUser(testUser);
        assertTrue(updateResult, "更新用户应该成功");

        User updatedUser = redisService.getUserById(999L);
        assertEquals(26, updatedUser.getAge(), "年龄应该已更新");

        // 测试用户是否存在
        boolean exists = redisService.userExists(999L);
        assertTrue(exists, "用户应该存在");

        // 测试删除用户
        boolean deleteResult = redisService.deleteUser(999L);
        assertTrue(deleteResult, "删除用户应该成功");

        // 验证用户已删除
        User deletedUser = redisService.getUserById(999L);
        assertNull(deletedUser, "删除后用户应该为空");
    }

    /**
     * 测试Redis连接状态
     */
    @Test
    public void testRedisConnection() {
        try {
            // 测试连接是否正常
            redisTemplate.opsForValue().set("connection:test", "OK");
            String result = (String) redisTemplate.opsForValue().get("connection:test");
            assertEquals("OK", result, "Redis连接测试失败");
            
            // 清理测试数据
            redisTemplate.delete("connection:test");
            
            System.out.println("Redis连接测试成功");
        } catch (Exception e) {
            fail("Redis连接测试失败: " + e.getMessage());
        }
    }
}

/**
 * 哨兵模式测试类
 */
@SpringBootTest
@TestPropertySource(properties = {
    "redis.mode=sentinel",
    "redis.sentinel.master=mymaster",
    "redis.sentinel.nodes=tasp-traffic.trinasolar.com:31014"
})
class RedisSentinelModeTest extends RedisConfigTest {
    
    @Test
    public void testSentinelMode() {
        assertEquals("sentinel", redisProperties.getMode(), "应该是哨兵模式");
        System.out.println("哨兵模式测试通过");
    }
}

/**
 * 集群模式测试类
 */
@SpringBootTest
@TestPropertySource(properties = {
    "redis.mode=cluster",
    "redis.cluster.nodes[0]=127.0.0.1:7000",
    "redis.cluster.nodes[1]=127.0.0.1:7001",
    "redis.cluster.nodes[2]=127.0.0.1:7002",
    "redis.cluster.max-redirects=3"
})
class RedisClusterModeTest extends RedisConfigTest {
    
    @Test
    public void testClusterMode() {
        assertEquals("cluster", redisProperties.getMode(), "应该是集群模式");
        System.out.println("集群模式测试通过");
    }
}
