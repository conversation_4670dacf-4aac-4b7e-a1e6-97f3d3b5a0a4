#!/bin/bash

echo "=== Redis集群模式实现验证 ==="
echo

echo "1. 检查项目结构..."
echo "✓ 配置文件:"
ls -la src/main/resources/application*.yml
echo

echo "✓ Java配置类:"
ls -la src/main/java/com/trinasloar/gateway/zeuspaasyz/config/
echo

echo "✓ 控制器:"
ls -la src/main/java/com/trinasloar/gateway/zeuspaasyz/controller/
echo

echo "2. 检查依赖..."
echo "✓ 检查pom.xml中的commons-pool2依赖:"
grep -A 3 "commons-pool2" pom.xml
echo

echo "3. 编译项目..."
mvn clean compile

if [ $? -eq 0 ]; then
    echo "✅ 编译成功!"
    echo
    echo "4. 运行测试..."
    mvn test -Dtest=RedisConfigTest
    
    if [ $? -eq 0 ]; then
        echo "✅ 测试通过!"
        echo
        echo "🎉 Redis集群模式实现完成!"
        echo
        echo "使用方式:"
        echo "- 哨兵模式: java -jar target/zeus-paas-yz-0.0.1-SNAPSHOT.jar --spring.profiles.active=sentinel"
        echo "- 集群模式: java -jar target/zeus-paas-yz-0.0.1-SNAPSHOT.jar --spring.profiles.active=cluster"
        echo "- 健康检查: curl http://localhost:8080/api/health/redis"
    else
        echo "❌ 测试失败，请检查Redis连接配置"
    fi
else
    echo "❌ 编译失败，请检查代码"
fi
